import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../database/base.repository';
import { SupabaseService } from '../database/supabase.service';
import { DatabaseUtils } from '../database/database.utils';
import {
  IUser,
  ICreateUserDto,
  IUpdateUserDto,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Injectable()
export class UserRepository extends BaseRepository<IUser> {
  protected readonly tableName = 'users';
  protected readonly entityName = 'User';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 根据openid查找用户
   */
  async findByOpenid(openid: string): Promise<IUser | null> {
    try {
      DatabaseUtils.logDatabaseOperation('FIND_BY_OPENID', this.tableName, {
        openid,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('openid', openid)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find user by openid',
        );
      }

      return result.data as IUser | null;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find user by openid', this.entityName);
    }
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<IUser | null> {
    try {
      if (!DatabaseUtils.isValidPhone(phone)) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_PHONE', this.tableName, {
        phone,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('phone', phone)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find user by phone',
        );
      }

      return result.data as IUser | null;
    } catch (error) {
      DatabaseUtils.handleError(error, 'find user by phone', this.entityName);
    }
  }

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<IUser | null> {
    try {
      if (!username || username.trim() === '') {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_USERNAME', this.tableName, {
        username,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('username', username)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find user by username',
        );
      }

      return result.data as IUser | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user by username',
        this.entityName,
      );
    }
  }

  /**
   * 创建用户
   */
  async createUser(userData: ICreateUserDto): Promise<IUser> {
    try {
      // 验证openid是否已存在
      const existingUser = await this.findByOpenid(userData.openid);
      if (existingUser) {
        throw new BusinessException(
          ErrorCode.USERNAME_TAKEN,
          'User with this openid already exists',
        );
      }

      // 如果提供了手机号，验证格式和唯一性
      if (userData.phone) {
        if (!DatabaseUtils.isValidPhone(userData.phone)) {
          throw new BusinessException(
            ErrorCode.INVALID_PHONE_FORMAT,
            'Invalid phone number format',
          );
        }

        const existingPhoneUser = await this.findByPhone(userData.phone);
        if (existingPhoneUser) {
          throw new BusinessException(
            ErrorCode.USERNAME_TAKEN,
            'User with this phone number already exists',
          );
        }
      }

      return await this.create(userData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'create user', this.entityName);
    }
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: string, userData: IUpdateUserDto): Promise<IUser> {
    try {
      // 如果更新手机号，验证格式和唯一性
      if (userData.phone) {
        if (!DatabaseUtils.isValidPhone(userData.phone)) {
          throw new BusinessException(
            ErrorCode.INVALID_PHONE_FORMAT,
            'Invalid phone number format',
          );
        }

        const existingPhoneUser = await this.findByPhone(userData.phone);
        if (existingPhoneUser && existingPhoneUser.id !== id) {
          throw new BusinessException(
            ErrorCode.USERNAME_TAKEN,
            'User with this phone number already exists',
          );
        }
      }

      return await this.update(id, userData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'update user', this.entityName);
    }
  }

  /**
   * 设置用户的默认宝宝
   */
  async setDefaultBaby(userId: string, babyId: string | null): Promise<IUser> {
    try {
      return await this.update(userId, {
        default_baby_id: babyId,
      } as IUpdateUserDto);
    } catch (error) {
      DatabaseUtils.handleError(error, 'set default baby', this.entityName);
    }
  }

  /**
   * 获取用户及其默认宝宝信息
   */
  async findUserWithDefaultBaby(userId: string): Promise<IUser | null> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_USER_WITH_DEFAULT_BABY',
        this.tableName,
        { userId },
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          default_baby:babies!users_default_baby_id_fkey(
            id,
            nickname,
            birth_date,
            gender,
            avatar_url
          )
        `,
        )
        .eq('id', userId)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find user with default baby',
        );
      }

      return result.data as IUser | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find user with default baby',
        this.entityName,
      );
    }
  }

  /**
   * 批量获取用户信息（优化性能）
   */
  async findUsersByIds(userIds: string[]): Promise<IUser[]> {
    try {
      if (!userIds.length) {
        return [];
      }

      // Filter valid UUIDs
      const validUserIds = userIds.filter((id) =>
        DatabaseUtils.isValidUUID(id),
      );

      if (!validUserIds.length) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_USERS_BY_IDS', this.tableName, {
        userIds: validUserIds,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .in('id', validUserIds);

      this.supabaseService.handleDatabaseError(
        result.error,
        'find users by ids',
      );

      return (result.data as IUser[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(error, 'find users by ids', this.entityName);
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IUser>): void {
    DatabaseUtils.validateRequiredFields(
      data,
      ['openid'] as (keyof IUser)[],
      this.entityName,
    );

    const createData = data as ICreateUserDto;

    if (!createData.openid || createData.openid.trim() === '') {
      throw new BusinessException(
        ErrorCode.INVALID_WECHAT_CODE,
        'OpenID is required and cannot be empty',
      );
    }
  }
}
