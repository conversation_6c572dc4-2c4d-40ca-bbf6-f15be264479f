import { Injectable } from '@nestjs/common';
import { BabyRepository } from './baby.repository';
import { UserRepository } from '../user/user.repository';
import { FamilyRepository } from '../family/family.repository';
import { CacheService } from '../common/services/cache.service';
import { PerformanceMonitorService } from '../common/services/performance-monitor.service';
import {
  IBaby,
  ICreateBabyDto,
  IUpdateBabyDto,
  IBabyResponse,
  IUser,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode } from '../common/enums';

@Injectable()
export class BabyService {
  constructor(
    private readonly babyRepository: BabyRepository,
    private readonly userRepository: UserRepository,
    private readonly familyRepository: FamilyRepository,
    private readonly cacheService: CacheService,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}

  /**
   * 获取缓存的用户信息
   */
  private async getCachedUser(userId: string): Promise<IUser | null> {
    const cacheKey = this.cacheService.generateUserKey(userId, 'profile');
    return this.cacheService.getOrSet(
      cacheKey,
      () => this.userRepository.findById(userId),
      2 * 60 * 1000, // 2 minutes TTL for user data
    );
  }

  /**
   * 创建宝宝档案
   */
  async createBaby(
    userId: string,
    createBabyDto: ICreateBabyDto & { role: number },
  ): Promise<IBabyResponse> {
    // 检查家庭中是否存在相同小名的宝宝
    const hasDuplicateNickname =
      await this.babyRepository.checkDuplicateNicknameInFamily(
        userId,
        createBabyDto.nickname,
      );
    if (hasDuplicateNickname) {
      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA,
        'A baby with this nickname already exists in your family',
        400,
      );
    }

    // 从 DTO 中提取 role，其余字段用于创建宝宝档案
    const { role, ...babyData } = createBabyDto;

    // 创建宝宝档案
    const baby = await this.babyRepository.createBaby({
      ...babyData,
      owner_id: userId,
    });

    // 创建家庭成员关系（所有者）
    await this.familyRepository.createFamilyMember({
      baby_id: baby.id,
      user_id: userId,
      role: role, // 使用传递的角色
      is_owner: true,
    });

    // 处理默认宝宝逻辑
    await this.handleDefaultBabyLogic(
      userId,
      baby.id,
      createBabyDto.is_default,
    );

    // Invalidate user cache since baby list changed
    this.cacheService.invalidateUserCache(userId);

    // 返回带有默认状态的宝宝信息
    return this.getBabyWithDefaultStatus(baby, userId);
  }

  /**
   * 获取用户有权限访问的所有宝宝
   */
  async getBabies(userId: string): Promise<IBabyResponse[]> {
    const timer = this.performanceMonitor.startTimer('BabyService.getBabies');

    try {
      // Fetch babies and user data in parallel to reduce latency
      const [babies, user] = await Promise.all([
        this.babyRepository.findAccessibleBabies(userId),
        this.getCachedUser(userId),
      ]);

      // Map babies with default status in a single pass
      const result = babies.map((baby) => ({
        ...baby,
        is_default: user?.default_baby_id === baby.id,
      }));

      timer({ userId, babiesCount: result.length, success: true });
      return result;
    } catch (error) {
      timer({
        userId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * 根据ID获取宝宝信息
   */
  async getBabyById(babyId: string, userId: string): Promise<IBabyResponse> {
    // Parallel execution for better performance
    const [hasAccess, baby] = await Promise.all([
      this.babyRepository.checkBabyAccess(babyId, userId),
      this.babyRepository.findById(babyId),
    ]);

    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to access this baby profile',
      );
    }

    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
      );
    }

    return this.getBabyWithDefaultStatus(baby, userId);
  }

  /**
   * 更新宝宝信息
   */
  async updateBaby(
    babyId: string,
    userId: string,
    updateBabyDto: IUpdateBabyDto,
  ): Promise<IBabyResponse> {
    // 检查访问权限
    const hasAccess = await this.babyRepository.checkBabyAccess(babyId, userId);
    if (!hasAccess) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'You do not have permission to update this baby profile',
      );
    }

    // 检查是否是所有者（只有所有者可以设置默认宝宝）
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (updateBabyDto.is_default !== undefined && !isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can change default baby setting',
      );
    }

    // 更新宝宝信息
    const updatedBaby = await this.babyRepository.updateBaby(
      babyId,
      updateBabyDto,
    );

    // 处理默认宝宝逻辑（仅所有者可以设置）
    if (updateBabyDto.is_default !== undefined && isOwner) {
      await this.handleDefaultBabyLogic(
        userId,
        babyId,
        updateBabyDto.is_default,
      );
    }

    return this.getBabyWithDefaultStatus(updatedBaby, userId);
  }

  /**
   * 删除宝宝档案
   */
  async deleteBaby(babyId: string, userId: string): Promise<void> {
    // 检查是否是所有者
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (!isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can delete the baby profile',
      );
    }

    const user = await this.userRepository.findById(userId);
    const isDefaultBaby = user?.default_baby_id === babyId;

    // 删除宝宝档案
    await this.babyRepository.deleteBaby(babyId, userId);

    // 如果删除的是默认宝宝，自动选择新的默认宝宝
    if (isDefaultBaby) {
      await this.selectNewDefaultBaby(userId);
    }
  }

  /**
   * 获取用户的默认宝宝
   */
  async getDefaultBaby(userId: string): Promise<IBabyResponse | null> {
    const user = await this.userRepository.findById(userId);
    if (!user?.default_baby_id) {
      return null;
    }

    const baby = await this.babyRepository.findById(user.default_baby_id);
    if (!baby) {
      // 如果默认宝宝不存在，清除用户的默认宝宝设置
      await this.userRepository.setDefaultBaby(userId, null);
      return null;
    }

    return {
      ...baby,
      is_default: true,
    };
  }

  /**
   * 设置默认宝宝
   */
  async setDefaultBaby(userId: string, babyId: string): Promise<void> {
    // 检查是否是所有者
    const isOwner = await this.babyRepository.checkBabyOwnership(
      babyId,
      userId,
    );
    if (!isOwner) {
      throw new BusinessException(
        ErrorCode.BABY_ACCESS_DENIED,
        'Only baby owner can set default baby',
      );
    }

    // 检查宝宝是否存在
    const baby = await this.babyRepository.findById(babyId);
    if (!baby) {
      throw new BusinessException(
        ErrorCode.BABY_NOT_FOUND,
        'Baby profile not found',
      );
    }

    // 设置默认宝宝
    await this.userRepository.setDefaultBaby(userId, babyId);
  }

  /**
   * 处理默认宝宝逻辑
   */
  private async handleDefaultBabyLogic(
    userId: string,
    babyId: string,
    isDefault?: boolean,
  ): Promise<void> {
    const user = await this.userRepository.findById(userId);
    const userBabies = await this.babyRepository.findByOwnerId(userId);

    // 如果这是用户的第一个宝宝，自动设为默认
    if (userBabies.length === 1) {
      await this.userRepository.setDefaultBaby(userId, babyId);
      return;
    }

    // 如果明确设置为默认宝宝
    if (isDefault === true) {
      await this.userRepository.setDefaultBaby(userId, babyId);
      return;
    }

    // 如果明确设置为非默认，且当前是默认宝宝，则选择新的默认宝宝
    if (isDefault === false && user?.default_baby_id === babyId) {
      await this.selectNewDefaultBaby(userId);
    }
  }

  /**
   * 选择新的默认宝宝（选择最早创建的宝宝）
   */
  private async selectNewDefaultBaby(userId: string): Promise<void> {
    const firstBaby = await this.babyRepository.findFirstBabyByOwner(userId);

    if (firstBaby) {
      await this.userRepository.setDefaultBaby(userId, firstBaby.id);
    } else {
      await this.userRepository.setDefaultBaby(userId, null);
    }
  }

  /**
   * 获取带有默认状态的宝宝信息
   */
  private async getBabyWithDefaultStatus(
    baby: IBaby,
    userId: string,
    user?: IUser | null,
  ): Promise<IBabyResponse> {
    // Use provided user data if available, otherwise fetch from cache
    const userData =
      user !== undefined ? user : await this.getCachedUser(userId);

    return {
      ...baby,
      is_default: userData?.default_baby_id === baby.id,
    };
  }

  /**
   * 获取用户拥有的宝宝数量
   */
  async getBabyCount(userId: string): Promise<number> {
    const babies = await this.babyRepository.findByOwnerId(userId);
    return babies.length;
  }

  /**
   * 检查用户是否达到宝宝数量限制
   */
  async checkBabyLimit(userId: string): Promise<boolean> {
    const count = await this.getBabyCount(userId);
    return count >= 5;
  }
}
