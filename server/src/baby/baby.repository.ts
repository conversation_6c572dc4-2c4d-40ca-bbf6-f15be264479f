import { Injectable } from '@nestjs/common';
import { BaseRepository } from '../database/base.repository';
import { SupabaseService } from '../database/supabase.service';
import { DatabaseUtils } from '../database/database.utils';
import {
  IBaby,
  ICreateBabyDto,
  IUpdateBabyDto,
} from '../common/interfaces/entities';
import { BusinessException } from '../common/exceptions/business.exception';
import { ErrorCode, Gender } from '../common/enums';

@Injectable()
export class BabyRepository extends BaseRepository<IBaby> {
  protected readonly tableName = 'babies';
  protected readonly entityName = 'Baby';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 根据用户ID查找宝宝列表
   */
  async findByOwnerId(ownerId: string): Promise<IBaby[]> {
    try {
      return await this.findMany({ owner_id: ownerId } as Partial<IBaby>, {
        sortBy: 'created_at',
        sortOrder: 'asc',
      });
    } catch (error) {
      DatabaseUtils.handleError(error, 'find babies by owner', this.entityName);
    }
  }

  /**
   * 获取用户有权限访问的所有宝宝（包括家庭成员权限）
   */
  async findAccessibleBabies(userId: string): Promise<IBaby[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ACCESSIBLE_BABIES',
        this.tableName,
        { userId },
      );

      // Optimized query: select only baby fields, avoid unnecessary join data
      const result = await this.supabaseService
        .query(this.tableName)
        .select(
          `
          id,
          owner_id,
          nickname,
          birth_date,
          gender,
          avatar_url,
          created_at,
          updated_at,
          family_members!inner(user_id)
        `,
        )
        .eq('family_members.user_id', userId)
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find accessible babies',
      );

      return (result.data as IBaby[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find accessible babies',
        this.entityName,
      );
    }
  }

  /**
   * 创建宝宝档案
   */
  async createBaby(
    babyData: ICreateBabyDto & { owner_id: string },
  ): Promise<IBaby> {
    try {
      // 检查用户宝宝数量限制（最多5个）
      const existingBabiesCount = await this.count({
        owner_id: babyData.owner_id,
      } as Partial<IBaby>);
      if (existingBabiesCount >= 5) {
        throw new BusinessException(
          ErrorCode.BABY_LIMIT_EXCEEDED,
          'Maximum 5 babies allowed per user',
        );
      }

      // 验证性别值
      if (babyData.gender && !Object.values(Gender).includes(babyData.gender)) {
        throw new BusinessException(
          ErrorCode.INVALID_BABY_DATA,
          'Invalid gender value',
        );
      }

      return await this.create(babyData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'create baby', this.entityName);
    }
  }

  /**
   * 更新宝宝信息
   */
  async updateBaby(id: string, babyData: IUpdateBabyDto): Promise<IBaby> {
    try {
      // 验证性别值
      if (babyData.gender && !Object.values(Gender).includes(babyData.gender)) {
        throw new BusinessException(
          ErrorCode.INVALID_BABY_DATA,
          'Invalid gender value',
        );
      }

      return await this.update(id, babyData);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'update baby', this.entityName);
    }
  }

  /**
   * 检查用户是否有权限访问宝宝
   */
  async checkBabyAccess(babyId: string, userId: string): Promise<boolean> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'CHECK_BABY_ACCESS',
        'family_members',
        { babyId, userId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .select('id', { count: 'exact', head: true })
        .eq('baby_id', babyId)
        .eq('user_id', userId);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check baby access', error);
      return false;
    }
  }

  /**
   * 检查用户是否是宝宝的所有者
   */
  async checkBabyOwnership(babyId: string, userId: string): Promise<boolean> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'CHECK_BABY_OWNERSHIP',
        'family_members',
        { babyId, userId },
      );

      const result = await this.supabaseService
        .query('family_members')
        .select('id', { count: 'exact', head: true })
        .eq('baby_id', babyId)
        .eq('user_id', userId)
        .eq('is_owner', true);

      return (result.count || 0) > 0;
    } catch (error) {
      this.logger.warn('Failed to check baby ownership', error);
      return false;
    }
  }

  /**
   * 删除宝宝档案（仅所有者可删除）
   */
  async deleteBaby(id: string, userId: string): Promise<boolean> {
    try {
      // 检查是否是所有者
      const isOwner = await this.checkBabyOwnership(id, userId);
      if (!isOwner) {
        throw new BusinessException(
          ErrorCode.BABY_ACCESS_DENIED,
          'Only baby owner can delete the baby profile',
        );
      }

      return await this.delete(id);
    } catch (error) {
      if (error instanceof BusinessException) {
        throw error;
      }
      DatabaseUtils.handleError(error, 'delete baby', this.entityName);
    }
  }

  /**
   * 获取用户的第一个宝宝（按创建时间）
   */
  async findFirstBabyByOwner(ownerId: string): Promise<IBaby | null> {
    try {
      const babies = await this.findMany(
        { owner_id: ownerId } as Partial<IBaby>,
        { sortBy: 'created_at', sortOrder: 'asc', limit: 1 },
      );

      return babies.length > 0 ? babies[0] : null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find first baby by owner',
        this.entityName,
      );
    }
  }

  /**
   * 检查用户家庭中是否存在相同小名的宝宝
   */
  async checkDuplicateNicknameInFamily(
    userId: string,
    nickname: string,
  ): Promise<boolean> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'CHECK_DUPLICATE_NICKNAME',
        this.tableName,
        { userId, nickname },
      );

      // 查找用户有权限访问的所有宝宝中是否有相同小名
      const result = await this.supabaseService
        .query(this.tableName)
        .select(
          `
          id,
          nickname,
          family_members!inner(user_id)
        `,
        )
        .eq('family_members.user_id', userId)
        .ilike('nickname', nickname);

      this.supabaseService.handleDatabaseError(
        result.error,
        'check duplicate nickname in family',
      );

      return (result.data?.length || 0) > 0;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'check duplicate nickname in family',
        this.entityName,
      );
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IBaby>): void {
    DatabaseUtils.validateRequiredFields(
      data,
      ['owner_id', 'nickname', 'birth_date', 'gender'] as (keyof IBaby)[],
      this.entityName,
    );

    const createData = data as ICreateBabyDto & { owner_id: string };

    if (!createData.nickname || createData.nickname.trim() === '') {
      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA,
        'Baby nickname is required and cannot be empty',
      );
    }

    if (!createData.birth_date) {
      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA,
        'Baby birth date is required',
      );
    }

    // 验证出生日期不能是未来
    const birthDate = new Date(createData.birth_date);
    if (birthDate > new Date()) {
      throw new BusinessException(
        ErrorCode.INVALID_BABY_DATA,
        'Baby birth date cannot be in the future',
      );
    }
  }
}
