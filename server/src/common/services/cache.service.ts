import { Injectable, Logger } from '@nestjs/common';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly cache = new Map<string, CacheItem<any>>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL

  /**
   * Set a value in cache with optional TTL
   */
  set<T>(key: string, value: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data: value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    };

    this.cache.set(key, item);
    this.logger.debug(`Cache set: ${key} (TTL: ${item.ttl}ms)`);
  }

  /**
   * Get a value from cache
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) {
      this.logger.debug(`Cache miss: ${key}`);
      return null;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.logger.debug(`Cache expired: ${key}`);
      return null;
    }

    this.logger.debug(`Cache hit: ${key}`);
    return item.data as T;
  }

  /**
   * Delete a value from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.logger.debug(`Cache deleted: ${key}`);
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.logger.debug('Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    keys: string[];
    expired: number;
  } {
    const now = Date.now();
    let expired = 0;
    const keys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expired++;
      } else {
        keys.push(key);
      }
    }

    return {
      size: this.cache.size,
      keys,
      expired,
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.logger.debug(`Cache cleanup: removed ${cleaned} expired entries`);
    }

    return cleaned;
  }

  /**
   * Get or set pattern - if key exists and not expired, return it, otherwise execute fn and cache result
   */
  async getOrSet<T>(
    key: string,
    fn: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const result = await fn();
    this.set(key, result, ttl);
    return result;
  }

  /**
   * Generate cache key for user-specific data
   */
  generateUserKey(userId: string, suffix: string): string {
    return `user:${userId}:${suffix}`;
  }

  /**
   * Generate cache key for baby-specific data
   */
  generateBabyKey(babyId: string, suffix: string): string {
    return `baby:${babyId}:${suffix}`;
  }

  /**
   * Generate cache key for habit statistics
   */
  generateHabitStatsKey(
    userId: string,
    babyId: string,
    type: string,
    date?: string,
  ): string {
    const dateStr = date || new Date().toISOString().split('T')[0];
    return `stats:${userId}:${babyId}:${type}:${dateStr}`;
  }

  /**
   * Invalidate all cache entries for a specific user
   */
  invalidateUserCache(userId: string): number {
    let invalidated = 0;
    const userPrefix = `user:${userId}:`;
    const statsPrefix = `stats:${userId}:`;

    for (const key of this.cache.keys()) {
      if (key.startsWith(userPrefix) || key.startsWith(statsPrefix)) {
        this.cache.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      this.logger.debug(`Invalidated ${invalidated} cache entries for user ${userId}`);
    }

    return invalidated;
  }

  /**
   * Invalidate all cache entries for a specific baby
   */
  invalidateBabyCache(babyId: string): number {
    let invalidated = 0;
    const babyPrefix = `baby:${babyId}:`;

    for (const key of this.cache.keys()) {
      if (key.startsWith(babyPrefix) || key.includes(`:${babyId}:`)) {
        this.cache.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      this.logger.debug(`Invalidated ${invalidated} cache entries for baby ${babyId}`);
    }

    return invalidated;
  }
}
