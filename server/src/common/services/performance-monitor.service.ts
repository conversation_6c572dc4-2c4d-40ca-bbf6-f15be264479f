import { Injectable, Logger } from '@nestjs/common';

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  operation: string;
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  lastExecuted: Date;
}

@Injectable()
export class PerformanceMonitorService {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  private readonly metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 metrics
  private readonly slowQueryThreshold = 1000; // Log queries slower than 1 second

  /**
   * Start timing an operation
   */
  startTimer(operation: string): (metadata?: Record<string, any>) => void {
    const startTime = Date.now();

    return (metadata?: Record<string, any>) => {
      const duration = Date.now() - startTime;
      this.recordMetric(operation, duration, metadata);
    };
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    operation: string,
    duration: number,
    metadata?: Record<string, any>,
  ): void {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      metadata,
    };

    // Add to metrics array
    this.metrics.push(metric);

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    // Log slow operations
    if (duration > this.slowQueryThreshold) {
      this.logger.warn(
        `Slow operation detected: ${operation} took ${duration}ms`,
        { metadata },
      );
    } else {
      this.logger.debug(
        `Operation completed: ${operation} took ${duration}ms`,
        { metadata },
      );
    }
  }

  /**
   * Get performance statistics for an operation
   */
  getOperationStats(operation: string): PerformanceStats | null {
    const operationMetrics = this.metrics.filter(
      (m) => m.operation === operation,
    );

    if (operationMetrics.length === 0) {
      return null;
    }

    const durations = operationMetrics.map((m) => m.duration);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);

    return {
      operation,
      count: operationMetrics.length,
      totalDuration,
      averageDuration: totalDuration / operationMetrics.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      lastExecuted: operationMetrics[operationMetrics.length - 1].timestamp,
    };
  }

  /**
   * Get all performance statistics
   */
  getAllStats(): PerformanceStats[] {
    const operationGroups = new Map<string, PerformanceMetric[]>();

    // Group metrics by operation
    this.metrics.forEach((metric) => {
      if (!operationGroups.has(metric.operation)) {
        operationGroups.set(metric.operation, []);
      }
      operationGroups.get(metric.operation)!.push(metric);
    });

    // Calculate stats for each operation
    const stats: PerformanceStats[] = [];
    operationGroups.forEach((metrics, operation) => {
      const durations = metrics.map((m) => m.duration);
      const totalDuration = durations.reduce((sum, d) => sum + d, 0);

      stats.push({
        operation,
        count: metrics.length,
        totalDuration,
        averageDuration: totalDuration / metrics.length,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations),
        lastExecuted: metrics[metrics.length - 1].timestamp,
      });
    });

    // Sort by average duration (slowest first)
    return stats.sort((a, b) => b.averageDuration - a.averageDuration);
  }

  /**
   * Get slow operations (above threshold)
   */
  getSlowOperations(): PerformanceMetric[] {
    return this.metrics.filter((m) => m.duration > this.slowQueryThreshold);
  }

  /**
   * Get recent metrics (last N minutes)
   */
  getRecentMetrics(minutes: number = 5): PerformanceMetric[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    return this.metrics.filter((m) => m.timestamp > cutoff);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.length = 0;
    this.logger.debug('Performance metrics cleared');
  }

  /**
   * Decorator for monitoring method performance
   */
  monitor(operation?: string) {
    return (
      target: any,
      propertyName: string,
      descriptor: PropertyDescriptor,
    ) => {
      const method = descriptor.value;
      const operationName = operation || `${target.constructor.name}.${propertyName}`;

      descriptor.value = async function (...args: any[]) {
        const timer = this.performanceMonitor?.startTimer?.(operationName) || 
                     (() => {}); // Fallback if service not injected

        try {
          const result = await method.apply(this, args);
          timer({ success: true, argsCount: args.length });
          return result;
        } catch (error) {
          timer({ success: false, error: error.message, argsCount: args.length });
          throw error;
        }
      };

      return descriptor;
    };
  }

  /**
   * Log performance summary
   */
  logPerformanceSummary(): void {
    const stats = this.getAllStats();
    const slowOps = this.getSlowOperations();

    this.logger.log('=== Performance Summary ===');
    this.logger.log(`Total operations tracked: ${this.metrics.length}`);
    this.logger.log(`Slow operations (>${this.slowQueryThreshold}ms): ${slowOps.length}`);

    if (stats.length > 0) {
      this.logger.log('Top 5 slowest operations:');
      stats.slice(0, 5).forEach((stat, index) => {
        this.logger.log(
          `${index + 1}. ${stat.operation}: avg ${stat.averageDuration.toFixed(2)}ms (${stat.count} calls)`,
        );
      });
    }

    this.logger.log('=== End Performance Summary ===');
  }

  /**
   * Set slow query threshold
   */
  setSlowQueryThreshold(threshold: number): void {
    this.logger.log(`Slow query threshold updated to ${threshold}ms`);
    (this as any).slowQueryThreshold = threshold;
  }
}
