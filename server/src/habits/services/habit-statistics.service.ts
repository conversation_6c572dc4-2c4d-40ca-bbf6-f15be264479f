import { Injectable, Logger } from '@nestjs/common';
import { UserHabitRepository } from '../repositories/user-habit.repository';
import { HabitCheckInRepository } from '../repositories/habit-checkin.repository';
import { CacheService } from '../../common/services/cache.service';
import { PerformanceMonitorService } from '../../common/services/performance-monitor.service';
import {
  IDailyStatistics,
  IWeeklyStatistics,
  IMonthlyStatistics,
  IOverviewStatistics,
  IHabitDetailStatistics,
} from '../interfaces/habit-statistics.interface';
import { IUserHabit } from '../interfaces/user-habit.interface';
import { IHabitCheckIn } from '../interfaces/habit-check-in.interface';

@Injectable()
export class HabitStatisticsService {
  private readonly logger = new Logger(HabitStatisticsService.name);

  constructor(
    private readonly userHabitRepository: UserHabitRepository,
    private readonly habitCheckInRepository: HabitCheckInRepository,
    private readonly cacheService: CacheService,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}

  /**
   * 获取日统计数据
   */
  async getDailyStatistics(
    userId: string,
    babyId: string,
    date: Date = new Date(),
  ): Promise<IDailyStatistics> {
    try {
      this.logger.log(
        `Getting daily statistics for user ${userId}, baby ${babyId}, date ${date.toISOString()}`,
      );

      // 获取指定日期应该执行的习惯
      const todayHabits = await this.getTodayHabits(userId, babyId, date);

      // 获取当日的打卡记录
      const startDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);

      const checkIns =
        (await this.habitCheckInRepository.findByDateRange(
          userId,
          babyId,
          startDate,
          endDate,
        )) || [];

      // 创建打卡记录映射
      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        checkInMap.set(checkIn.habit_id, checkIn);
      });

      // 构建习惯完成状态
      const habits = todayHabits.map((habit) => {
        const checkIn = checkInMap.get(habit.id);
        return {
          habit_id: habit.id,
          habit_name: habit.name,
          habit_icon: habit.icon,
          theme_color: habit.theme_color,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
        };
      });

      // 计算完成率
      const completedCount = habits.filter((h) => h.is_completed).length;
      const totalCount = habits.length;
      const completion_rate = totalCount > 0 ? completedCount / totalCount : 0;

      return {
        date,
        habits,
        completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get daily statistics', error);
      throw error;
    }
  }

  /**
   * 获取周统计数据
   */
  async getWeeklyStatistics(
    userId: string,
    babyId: string,
    weekStart: Date,
  ): Promise<IWeeklyStatistics> {
    try {
      this.logger.log(
        `Getting weekly statistics for user ${userId}, baby ${babyId}, week start ${weekStart.toISOString()}`,
      );

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 6);

      // Fetch all habits and check-ins for the entire week in parallel
      const [allHabits, weekCheckIns] = await Promise.all([
        this.userHabitRepository.findActiveHabits(userId, babyId),
        this.habitCheckInRepository.findCheckInsByDateRangeOptimized(
          userId,
          babyId,
          weekStart,
          new Date(weekEnd.getTime() + 24 * 60 * 60 * 1000), // Add 1 day for inclusive end
        ),
      ]);

      // Group check-ins by date for efficient lookup
      const checkInsByDate = new Map<string, Set<string>>();
      weekCheckIns.forEach((checkIn) => {
        const dateKey = new Date(checkIn.check_in_date)
          .toISOString()
          .split('T')[0];
        if (!checkInsByDate.has(dateKey)) {
          checkInsByDate.set(dateKey, new Set());
        }
        checkInsByDate.get(dateKey)!.add(checkIn.habit_id);
      });

      const daily_completion: Array<{
        date: Date;
        completion_rate: number;
        completed_count: number;
        total_count: number;
      }> = [];
      let totalCompletedCount = 0;
      let totalHabitCount = 0;

      // Calculate daily statistics for each day
      for (let i = 0; i < 7; i++) {
        const currentDate = new Date(weekStart);
        currentDate.setDate(currentDate.getDate() + i);
        const dateKey = currentDate.toISOString().split('T')[0];

        // Get habits scheduled for this day
        const dayOfWeek = currentDate.getDay() === 0 ? 7 : currentDate.getDay();
        const todayHabits = allHabits.filter(
          (habit) => habit.frequency && habit.frequency.includes(dayOfWeek),
        );

        // Count completed habits for this day
        const completedHabits = checkInsByDate.get(dateKey) || new Set();
        const completedCount = todayHabits.filter((habit) =>
          completedHabits.has(habit.id),
        ).length;
        const totalCount = todayHabits.length;

        const completion_rate =
          totalCount > 0 ? completedCount / totalCount : 0;

        daily_completion.push({
          date: currentDate,
          completion_rate,
          completed_count: completedCount,
          total_count: totalCount,
        });

        totalCompletedCount += completedCount;
        totalHabitCount += totalCount;
      }

      // Calculate overall completion rate
      const overall_completion_rate =
        totalHabitCount > 0 ? totalCompletedCount / totalHabitCount : 0;

      return {
        week_start: weekStart,
        week_end: weekEnd,
        daily_completion,
        overall_completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get weekly statistics', error);
      throw error;
    }
  }

  /**
   * 获取月统计数据
   */
  async getMonthlyStatistics(
    userId: string,
    babyId: string,
    year: number,
    month: number,
  ): Promise<IMonthlyStatistics> {
    try {
      this.logger.log(
        `Getting monthly statistics for user ${userId}, baby ${babyId}, year ${year}, month ${month}`,
      );

      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      const daysInMonth = endDate.getDate();
      const today = new Date();

      // Fetch all habits and check-ins for the entire month in parallel
      const monthEndDate = new Date(endDate.getTime() + 24 * 60 * 60 * 1000);
      const [allHabits, monthCheckIns] = await Promise.all([
        this.userHabitRepository.findActiveHabits(userId, babyId),
        this.habitCheckInRepository.findCheckInsByDateRangeOptimized(
          userId,
          babyId,
          startDate,
          monthEndDate,
        ),
      ]);

      // Group check-ins by date for efficient lookup
      const checkInsByDate = new Map<string, Set<string>>();
      monthCheckIns.forEach((checkIn) => {
        const dateKey = new Date(checkIn.check_in_date)
          .toISOString()
          .split('T')[0];
        if (!checkInsByDate.has(dateKey)) {
          checkInsByDate.set(dateKey, new Set());
        }
        checkInsByDate.get(dateKey)!.add(checkIn.habit_id);
      });

      const calendar_data: Array<{
        date: Date;
        completion_rate: number;
        completed_count: number;
        total_count: number;
      }> = [];
      let totalCompletedCount = 0;
      let totalHabitCount = 0;

      // Calculate daily statistics for each day in the month
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(year, month - 1, day);
        const dateKey = currentDate.toISOString().split('T')[0];

        // Only calculate stats for dates up to today
        if (currentDate > today) {
          calendar_data.push({
            date: currentDate,
            completion_rate: 0,
            completed_count: 0,
            total_count: 0,
          });
          continue;
        }

        // Get habits scheduled for this day
        const dayOfWeek = currentDate.getDay() === 0 ? 7 : currentDate.getDay();
        const todayHabits = allHabits.filter(
          (habit) => habit.frequency && habit.frequency.includes(dayOfWeek),
        );

        // Count completed habits for this day
        const completedHabits = checkInsByDate.get(dateKey) || new Set();
        const completedCount = todayHabits.filter((habit) =>
          completedHabits.has(habit.id),
        ).length;
        const totalCount = todayHabits.length;

        const completion_rate =
          totalCount > 0 ? completedCount / totalCount : 0;

        calendar_data.push({
          date: currentDate,
          completion_rate,
          completed_count: completedCount,
          total_count: totalCount,
        });

        totalCompletedCount += completedCount;
        totalHabitCount += totalCount;
      }

      // Calculate overall completion rate
      const overall_completion_rate =
        totalHabitCount > 0 ? totalCompletedCount / totalHabitCount : 0;

      return {
        year,
        month,
        calendar_data,
        overall_completion_rate,
      };
    } catch (error) {
      this.logger.error('Failed to get monthly statistics', error);
      throw error;
    }
  }

  /**
   * 获取首页概览统计
   */
  async getOverviewStatistics(
    userId: string,
    babyId: string,
  ): Promise<IOverviewStatistics> {
    const timer = this.performanceMonitor.startTimer(
      'HabitStatisticsService.getOverviewStatistics',
    );

    try {
      this.logger.log(
        `Getting overview statistics for user ${userId}, baby ${babyId}`,
      );

      const today = new Date();
      const cacheKey = this.cacheService.generateHabitStatsKey(
        userId,
        babyId,
        'overview',
        today.toISOString().split('T')[0],
      );

      // Try to get from cache first
      const cached = this.cacheService.get<IOverviewStatistics>(cacheKey);
      if (cached) {
        timer({ userId, babyId, cached: true, success: true });
        return cached;
      }

      // Fetch all required data in parallel to reduce latency
      const [todayHabits, checkIns] = await Promise.all([
        this.getTodayHabits(userId, babyId, today),
        this.getTodayCheckIns(userId, babyId, today),
      ]);

      // Create check-in lookup map for O(1) access
      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        checkInMap.set(checkIn.habit_id, checkIn);
      });

      // Build timeline with completion status in a single pass
      const today_timeline = todayHabits.map((habit) => {
        const checkIn = checkInMap.get(habit.id);
        return {
          habit_id: habit.id,
          habit_name: habit.name,
          habit_icon: habit.icon,
          theme_color: habit.theme_color,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
          preferred_time: habit.preferred_time,
        };
      });

      // Sort by preferred time (habits without preferred time go last)
      today_timeline.sort((a, b) => {
        if (!a.preferred_time && !b.preferred_time) return 0;
        if (!a.preferred_time) return 1;
        if (!b.preferred_time) return -1;
        return a.preferred_time.localeCompare(b.preferred_time);
      });

      // Calculate completion metrics
      const completedCount = today_timeline.filter(
        (h) => h.is_completed,
      ).length;
      const totalCount = today_timeline.length;
      const completion_rate = totalCount > 0 ? completedCount / totalCount : 0;

      const result: IOverviewStatistics = {
        today_completion_rate: completion_rate,
        today_completed_count: completedCount,
        today_total_count: totalCount,
        today_timeline,
      };

      // Cache the result for 5 minutes
      this.cacheService.set(cacheKey, result, 5 * 60 * 1000);

      timer({
        userId,
        babyId,
        cached: false,
        habitsCount: result.today_total_count,
        success: true,
      });
      return result;
    } catch (error) {
      timer({
        userId,
        babyId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      this.logger.error('Failed to get overview statistics', error);
      throw error;
    }
  }

  /**
   * 获取习惯详情统计
   */
  async getHabitDetailStatistics(
    habitId: string,
    userId: string,
    babyId: string,
    year?: number,
    month?: number,
  ): Promise<IHabitDetailStatistics> {
    try {
      this.logger.log(
        `Getting habit detail statistics for habit ${habitId}, user ${userId}, baby ${babyId}`,
      );

      // 使用当前年月作为默认值
      const now = new Date();
      const targetYear = year || now.getFullYear();
      const targetMonth = month || now.getMonth() + 1;

      // 获取当月数据
      const startDate = new Date(targetYear, targetMonth - 1, 1);
      const endDate = new Date(targetYear, targetMonth, 0);
      const daysInMonth = endDate.getDate();

      const current_month_data: Array<{
        date: Date;
        is_completed: boolean;
        check_in_time?: Date;
        notes?: string;
      }> = [];

      // 获取当月的打卡记录
      const checkIns = await this.habitCheckInRepository.findByDateRange(
        userId,
        babyId,
        startDate,
        endDate,
        habitId,
      );

      const checkInMap = new Map<string, IHabitCheckIn>();
      checkIns.forEach((checkIn) => {
        const dateKey = new Date(checkIn.check_in_date)
          .toISOString()
          .split('T')[0];
        checkInMap.set(dateKey, checkIn);
      });

      // 构建每日数据
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(targetYear, targetMonth - 1, day);
        const dateKey = currentDate.toISOString().split('T')[0];
        const checkIn = checkInMap.get(dateKey);

        current_month_data.push({
          date: currentDate,
          is_completed: !!checkIn,
          check_in_time: checkIn?.check_in_time,
          notes: checkIn?.notes,
        });
      }

      // 计算完成率
      const completedDays = current_month_data.filter(
        (d) => d.is_completed,
      ).length;
      const totalDays = current_month_data.length;
      const completion_rate = totalDays > 0 ? completedDays / totalDays : 0;

      // 计算连续天数
      const streak_days = await this.habitCheckInRepository.getStreakDays(
        habitId,
        userId,
        babyId,
      );

      // 计算总打卡次数
      const total_check_ins = await this.habitCheckInRepository.countCheckIns(
        habitId,
        userId,
        babyId,
      );

      return {
        habit_id: habitId,
        current_month_data,
        completion_rate,
        streak_days,
        total_check_ins,
      };
    } catch (error) {
      this.logger.error('Failed to get habit detail statistics', error);
      throw error;
    }
  }

  /**
   * 获取指定日期应该执行的习惯
   */
  private async getTodayHabits(
    userId: string,
    babyId: string,
    date: Date,
  ): Promise<IUserHabit[]> {
    try {
      // 获取用户的所有活跃习惯
      const allHabits =
        (await this.userHabitRepository.findActiveHabits(userId, babyId)) || [];

      // 获取指定日期是周几 (1-7, 周一到周日)
      const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();

      // 筛选出当天应该执行的习惯
      return allHabits.filter(
        (habit) => habit.frequency && habit.frequency.includes(dayOfWeek),
      );
    } catch (error) {
      this.logger.error('Failed to get today habits', error);
      return [];
    }
  }

  /**
   * 获取今日的打卡记录
   */
  private async getTodayCheckIns(
    userId: string,
    babyId: string,
    date: Date,
  ): Promise<IHabitCheckIn[]> {
    try {
      const startDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 1);

      return (
        (await this.habitCheckInRepository.findByDateRange(
          userId,
          babyId,
          startDate,
          endDate,
        )) || []
      );
    } catch (error) {
      this.logger.error('Failed to get today check-ins', error);
      return [];
    }
  }
}
