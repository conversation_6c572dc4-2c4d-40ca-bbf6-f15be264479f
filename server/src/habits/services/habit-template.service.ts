import { Injectable, HttpStatus } from '@nestjs/common';
import { HabitTemplateRepository } from '../repositories/habit-template.repository';
import { HabitCategoryRepository } from '../repositories/habit-category.repository';
import { BusinessException } from '../../common/exceptions/business.exception';
import { ErrorCode } from '../../common/enums';
import {
  IHabitTemplate,
  ICreateHabitTemplate,
  IUpdateHabitTemplate,
} from '../interfaces/habit-template.interface';
import { CreateHabitTemplateDto } from '../dto/habit-template/create-habit-template.dto';
import { UpdateHabitTemplateDto } from '../dto/habit-template/update-habit-template.dto';
import { HabitTemplateResponseDto } from '../dto/habit-template/habit-template-response.dto';
import { HabitTemplateQueryDto } from '../dto/habit-template/habit-template-query.dto';

@Injectable()
export class HabitTemplateService {
  constructor(
    private readonly habitTemplateRepository: HabitTemplateRepository,
    private readonly habitCategoryRepository: HabitCategoryRepository,
  ) {}

  /**
   * 获取所有习惯模板
   */
  async findAll(
    query: HabitTemplateQueryDto,
  ): Promise<HabitTemplateResponseDto[]> {
    const {
      category_id,
      search,
      include_inactive,
      current,
      pageSize,
      sort_by,
      sort_order,
    } = query;

    let templates: (IHabitTemplate & { category?: any })[];

    if (search) {
      templates =
        await this.habitTemplateRepository.searchTemplatesWithCategories(
          search,
          category_id,
          {
            page: current,
            limit: pageSize,
            sortBy: sort_by,
            sortOrder: sort_order,
          },
        );
    } else if (category_id) {
      templates =
        await this.habitTemplateRepository.findByCategoryWithCategories(
          category_id,
          include_inactive,
        );
    } else {
      if (include_inactive) {
        templates = await this.habitTemplateRepository.findAllWithCategories({
          page: current,
          limit: pageSize,
          sortBy: sort_by,
          sortOrder: sort_order,
        });
      } else {
        templates =
          await this.habitTemplateRepository.findActiveTemplatesWithCategories();
      }
    }

    return templates.map((template) => this.mapToResponseDto(template));
  }

  /**
   * 根据ID获取习惯模板
   */
  async findById(id: string): Promise<HabitTemplateResponseDto> {
    const template =
      await this.habitTemplateRepository.findByIdWithCategory(id);

    if (!template) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.mapToResponseDto(template);
  }

  /**
   * 根据key获取习惯模板
   */
  async findByKey(key: string): Promise<HabitTemplateResponseDto> {
    const template = await this.habitTemplateRepository.findByKey(key);

    if (!template) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.mapToResponseDto(template);
  }

  /**
   * 创建习惯模板
   */
  async create(
    createDto: CreateHabitTemplateDto,
  ): Promise<HabitTemplateResponseDto> {
    // 验证分类是否存在
    const category = await this.habitCategoryRepository.findById(
      createDto.category_id,
    );
    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 检查同分类下是否存在同名模板
    const existingTemplate = await this.habitTemplateRepository.findByName(
      createDto.name,
      createDto.category_id,
    );

    if (existingTemplate) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NAME_EXISTS,
        '该分类下已存在同名的习惯模板',
        HttpStatus.CONFLICT,
      );
    }

    const createData: ICreateHabitTemplate = {
      category_id: createDto.category_id,
      name: createDto.name,
      icon: createDto.icon,
      theme_color: createDto.theme_color,
      description: createDto.description || '',
      is_active: createDto.is_active ?? true,
    };

    const template =
      await this.habitTemplateRepository.createTemplate(createData);
    return this.mapToResponseDto(template);
  }

  /**
   * 更新习惯模板
   */
  async update(
    id: string,
    updateDto: UpdateHabitTemplateDto,
  ): Promise<HabitTemplateResponseDto> {
    // 验证模板是否存在
    const existingTemplate = await this.habitTemplateRepository.findById(id);
    if (!existingTemplate) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    // 如果更新分类，验证新分类是否存在
    if (
      updateDto.category_id &&
      updateDto.category_id !== existingTemplate.category_id
    ) {
      const category = await this.habitCategoryRepository.findById(
        updateDto.category_id,
      );
      if (!category) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          '习惯分类不存在',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // 如果更新名称，检查是否重复
    if (updateDto.name && updateDto.name !== existingTemplate.name) {
      const categoryId = updateDto.category_id || existingTemplate.category_id;
      const duplicateTemplate = await this.habitTemplateRepository.findByName(
        updateDto.name,
        categoryId,
      );

      if (duplicateTemplate && duplicateTemplate.id !== id) {
        throw new BusinessException(
          ErrorCode.HABIT_TEMPLATE_NAME_EXISTS,
          '该分类下已存在同名的习惯模板',
          HttpStatus.CONFLICT,
        );
      }
    }

    const updateData: IUpdateHabitTemplate = {
      category_id: updateDto.category_id,
      name: updateDto.name,
      icon: updateDto.icon,
      theme_color: updateDto.theme_color,
      description: updateDto.description,
      is_active: updateDto.is_active,
    };

    const template = await this.habitTemplateRepository.updateTemplate(
      id,
      updateData,
    );
    return this.mapToResponseDto(template);
  }

  /**
   * 删除习惯模板
   */
  async delete(id: string): Promise<void> {
    const template = await this.habitTemplateRepository.findById(id);
    if (!template) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    await this.habitTemplateRepository.delete(id);
  }

  /**
   * 根据分类获取模板
   */
  async findByCategory(
    categoryId: string,
    includeInactive = false,
  ): Promise<HabitTemplateResponseDto[]> {
    // 验证分类是否存在
    const category = await this.habitCategoryRepository.findById(categoryId);
    if (!category) {
      throw new BusinessException(
        ErrorCode.HABIT_CATEGORY_NOT_FOUND,
        '习惯分类不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const templates =
      await this.habitTemplateRepository.findByCategoryWithCategories(
        categoryId,
        includeInactive,
      );
    return templates.map((template) => this.mapToResponseDto(template));
  }

  /**
   * 🚀 性能优化：根据多个分类ID批量获取模板
   */
  async findByCategoryIds(
    categoryIds: string[],
    includeInactive = false,
  ): Promise<HabitTemplateResponseDto[]> {
    if (categoryIds.length === 0) {
      return [];
    }

    const templates =
      await this.habitTemplateRepository.findByCategoryIds(
        categoryIds,
        includeInactive,
      );
    return templates.map((template: any) => this.mapToResponseDto(template));
  }

  /**
   * 批量创建模板
   */
  async createBatch(
    templates: CreateHabitTemplateDto[],
  ): Promise<HabitTemplateResponseDto[]> {
    if (!templates || templates.length === 0) {
      return [];
    }

    // 验证所有分类是否存在
    const categoryIds = [...new Set(templates.map((t) => t.category_id))];
    for (const categoryId of categoryIds) {
      const category = await this.habitCategoryRepository.findById(categoryId);
      if (!category) {
        throw new BusinessException(
          ErrorCode.HABIT_CATEGORY_NOT_FOUND,
          `习惯分类不存在: ${categoryId}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // 检查名称重复
    for (const template of templates) {
      const existing = await this.habitTemplateRepository.findByName(
        template.name,
        template.category_id,
      );
      if (existing) {
        throw new BusinessException(
          ErrorCode.HABIT_TEMPLATE_NAME_EXISTS,
          `模板名称已存在: ${template.name}`,
          HttpStatus.CONFLICT,
        );
      }
    }

    const createData: ICreateHabitTemplate[] = templates.map((dto) => ({
      category_id: dto.category_id,
      name: dto.name,
      icon: dto.icon,
      theme_color: dto.theme_color,
      description: dto.description || '',
      is_active: dto.is_active ?? true,
    }));

    const createdTemplates =
      await this.habitTemplateRepository.createBatch(createData);
    return createdTemplates.map((template) => this.mapToResponseDto(template));
  }

  /**
   * 激活模板
   */
  async activate(id: string): Promise<HabitTemplateResponseDto> {
    const template = await this.habitTemplateRepository.findById(id);
    if (!template) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedTemplate = await this.habitTemplateRepository.updateTemplate(
      id,
      { is_active: true },
    );
    return this.mapToResponseDto(updatedTemplate);
  }

  /**
   * 停用模板
   */
  async deactivate(id: string): Promise<HabitTemplateResponseDto> {
    const template = await this.habitTemplateRepository.findById(id);
    if (!template) {
      throw new BusinessException(
        ErrorCode.HABIT_TEMPLATE_NOT_FOUND,
        '习惯模板不存在',
        HttpStatus.NOT_FOUND,
      );
    }

    const updatedTemplate = await this.habitTemplateRepository.updateTemplate(
      id,
      { is_active: false },
    );
    return this.mapToResponseDto(updatedTemplate);
  }

  /**
   * 将实体映射为响应DTO
   */
  private mapToResponseDto(
    template: IHabitTemplate & {
      category?: { id: string; name: string; icon: string };
    },
  ): HabitTemplateResponseDto {
    const dto = new HabitTemplateResponseDto();
    dto.id = template.id;
    dto.category_id = template.category_id;
    dto.name = template.name;
    dto.icon = template.icon;
    dto.theme_color = template.theme_color;
    dto.description = template.description;
    dto.cover = template.cover;
    dto.is_active = template.is_active;
    dto.created_at = template.created_at;
    dto.updated_at = template.updated_at;

    if (template.category) {
      dto.category = {
        id: template.category.id,
        name: template.category.name,
        icon: template.category.icon,
      };
    }

    return dto;
  }
}
