import { Injectable } from '@nestjs/common';
import { BaseRepository, QueryOptions } from '../../database/base.repository';
import { SupabaseService } from '../../database/supabase.service';
import { DatabaseUtils } from '../../database/database.utils';
import {
  IHabitTemplate,
  ICreateHabitTemplate,
  IUpdateHabitTemplate,
} from '../interfaces/habit-template.interface';

@Injectable()
export class HabitTemplateRepository extends BaseRepository<IHabitTemplate> {
  protected readonly tableName = 'habit_templates';
  protected readonly entityName = 'habit template';

  constructor(supabaseService: SupabaseService) {
    super(supabaseService);
  }

  /**
   * 创建习惯模板
   */
  async createTemplate(data: ICreateHabitTemplate): Promise<IHabitTemplate> {
    return this.create({
      ...data,
      is_active: data.is_active ?? true,
    });
  }

  /**
   * 更新习惯模板
   */
  async updateTemplate(
    id: string,
    data: IUpdateHabitTemplate,
  ): Promise<IHabitTemplate> {
    return this.update(id, data);
  }

  /**
   * 获取所有活跃的模板
   */
  async findActiveTemplates(): Promise<IHabitTemplate[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ACTIVE_TEMPLATES',
        this.tableName,
        {},
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find active habit templates',
      );

      return (result.data as IHabitTemplate[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find active habit templates',
        this.entityName,
      );
    }
  }

  /**
   * 获取所有模板（包含分类信息）
   */
  async findAllWithCategories(
    options: QueryOptions = {},
  ): Promise<(IHabitTemplate & { category?: any })[]> {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ALL_WITH_CATEGORIES',
        this.tableName,
        options,
      );

      let query = this.supabaseService.query(this.tableName).select(`
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `);

      // 应用排序
      const { sortBy = 'created_at', sortOrder = 'asc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find all habit templates with categories',
      );

      return (result.data as (IHabitTemplate & { category?: any })[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find all habit templates with categories',
        this.entityName,
      );
    }
  }

  /**
   * 获取活跃模板（包含分类信息）
   */
  async findActiveTemplatesWithCategories(): Promise<
    (IHabitTemplate & { category?: any })[]
  > {
    try {
      DatabaseUtils.logDatabaseOperation(
        'FIND_ACTIVE_TEMPLATES_WITH_CATEGORIES',
        this.tableName,
        {},
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `,
        )
        .eq('is_active', true)
        .order('created_at', { ascending: true });

      this.supabaseService.handleDatabaseError(
        result.error,
        'find active habit templates with categories',
      );

      return (result.data as (IHabitTemplate & { category?: any })[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find active habit templates with categories',
        this.entityName,
      );
    }
  }

  /**
   * 根据分类ID获取模板
   */
  async findByCategory(
    categoryId: string,
    includeInactive = false,
  ): Promise<IHabitTemplate[]> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_CATEGORY', this.tableName, {
        categoryId,
        includeInactive,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('category_id', categoryId);

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      query = query.order('created_at', { ascending: true });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find habit templates by category',
      );

      return (result.data as IHabitTemplate[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit templates by category',
        this.entityName,
      );
    }
  }

  /**
   * 根据名称查找模板（用于检查重复）
   */
  async findByName(
    name: string,
    categoryId?: string,
  ): Promise<IHabitTemplate | null> {
    try {
      if (!name?.trim()) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_NAME', this.tableName, {
        name,
        categoryId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('name', name.trim());

      if (categoryId && DatabaseUtils.isValidUUID(categoryId)) {
        query = query.eq('category_id', categoryId);
      }

      const result = await query.single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find habit template by name',
        );
      }

      return result.data as IHabitTemplate | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit template by name',
        this.entityName,
      );
    }
  }

  /**
   * 根据key查找模板
   */
  async findByKey(key: string): Promise<IHabitTemplate | null> {
    try {
      if (!key?.trim()) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation('FIND_BY_KEY', this.tableName, {
        key,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('key', key.trim())
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find habit template by key',
        );
      }

      return result.data as IHabitTemplate | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit template by key',
        this.entityName,
      );
    }
  }

  /**
   * 获取模板详情（包含分类信息）
   */
  async findByIdWithCategory(
    id: string,
  ): Promise<(IHabitTemplate & { category?: any }) | null> {
    try {
      if (!DatabaseUtils.isValidUUID(id)) {
        return null;
      }

      DatabaseUtils.logDatabaseOperation(
        'FIND_BY_ID_WITH_CATEGORY',
        this.tableName,
        { id },
      );

      const result = await this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `,
        )
        .eq('id', id)
        .single();

      if (result.error && result.error.code !== 'PGRST116') {
        this.supabaseService.handleDatabaseError(
          result.error,
          'find habit template with category',
        );
      }

      return result.data as (IHabitTemplate & { category?: any }) | null;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit template with category',
        this.entityName,
      );
    }
  }

  /**
   * 获取分类下的模板数量
   */
  async countByCategory(categoryId: string): Promise<number> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return 0;
      }

      DatabaseUtils.logDatabaseOperation('COUNT_BY_CATEGORY', this.tableName, {
        categoryId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('category_id', categoryId);

      this.supabaseService.handleDatabaseError(
        result.error,
        'count habit templates by category',
      );

      return result.count || 0;
    } catch (error) {
      this.logger.warn('Failed to count templates by category', error);
      return 0;
    }
  }

  /**
   * 批量创建模板
   */
  async createBatch(
    templates: ICreateHabitTemplate[],
  ): Promise<IHabitTemplate[]> {
    try {
      if (!templates || templates.length === 0) {
        return [];
      }

      // 验证所有模板数据
      templates.forEach((template, index) => {
        try {
          this.validateCreateData(template);
        } catch (error) {
          throw new Error(`Template at index ${index}: ${error.message}`);
        }
      });

      const cleanTemplates = templates.map((template) => ({
        ...DatabaseUtils.cleanRecord(template),
        is_active: template.is_active ?? true,
      }));

      DatabaseUtils.logDatabaseOperation('CREATE_BATCH', this.tableName, {
        count: cleanTemplates.length,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .insert(cleanTemplates)
        .select();

      this.supabaseService.handleDatabaseError(
        result.error,
        'batch create habit templates',
      );

      this.logger.log(
        `${cleanTemplates.length} habit templates created successfully`,
      );
      return (result.data as IHabitTemplate[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'batch create habit templates',
        this.entityName,
      );
    }
  }

  /**
   * 根据分类删除所有模板
   */
  async deleteByCategory(categoryId: string): Promise<boolean> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return false;
      }

      DatabaseUtils.logDatabaseOperation('DELETE_BY_CATEGORY', this.tableName, {
        categoryId,
      });

      const result = await this.supabaseService
        .query(this.tableName)
        .delete()
        .eq('category_id', categoryId);

      this.supabaseService.handleDatabaseError(
        result.error,
        'delete habit templates by category',
      );

      this.logger.log(`Habit templates deleted for category ${categoryId}`);
      return true;
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'delete habit templates by category',
        this.entityName,
      );
    }
  }

  /**
   * 搜索模板
   */
  async searchTemplates(
    searchTerm: string,
    categoryId?: string,
    options: QueryOptions = {},
  ): Promise<IHabitTemplate[]> {
    try {
      if (!searchTerm?.trim()) {
        return categoryId
          ? await this.findByCategory(categoryId)
          : await this.findActiveTemplates();
      }

      DatabaseUtils.logDatabaseOperation('SEARCH_TEMPLATES', this.tableName, {
        searchTerm,
        categoryId,
      });

      let query = this.supabaseService
        .query(this.tableName)
        .select('*')
        .eq('is_active', true);

      if (categoryId && DatabaseUtils.isValidUUID(categoryId)) {
        query = query.eq('category_id', categoryId);
      }

      // 使用 ilike 进行模糊搜索（名称和描述）
      query = query.or(
        `name.ilike.%${searchTerm.trim()}%,description.ilike.%${searchTerm.trim()}%`,
      );

      // 应用排序
      const { sortBy = 'created_at', sortOrder = 'asc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'search habit templates',
      );

      return (result.data as IHabitTemplate[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'search habit templates',
        this.entityName,
      );
    }
  }

  /**
   * 搜索模板（包含分类信息）
   */
  async searchTemplatesWithCategories(
    searchTerm: string,
    categoryId?: string,
    options: QueryOptions = {},
  ): Promise<(IHabitTemplate & { category?: any })[]> {
    try {
      if (!searchTerm?.trim()) {
        return categoryId
          ? await this.findByCategoryWithCategories(categoryId)
          : await this.findActiveTemplatesWithCategories();
      }

      DatabaseUtils.logDatabaseOperation(
        'SEARCH_TEMPLATES_WITH_CATEGORIES',
        this.tableName,
        {
          searchTerm,
          categoryId,
        },
      );

      let query = this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `,
        )
        .eq('is_active', true);

      if (categoryId && DatabaseUtils.isValidUUID(categoryId)) {
        query = query.eq('category_id', categoryId);
      }

      // 使用 ilike 进行模糊搜索（名称和描述）
      query = query.or(
        `name.ilike.%${searchTerm.trim()}%,description.ilike.%${searchTerm.trim()}%`,
      );

      // 应用排序
      const { sortBy = 'created_at', sortOrder = 'asc' } = options;
      const { column, ascending } = DatabaseUtils.handleSorting(
        sortBy,
        sortOrder,
      );
      query = query.order(column, { ascending });

      // 应用分页
      if (options.page && options.limit) {
        const { from, to } = DatabaseUtils.handlePagination(
          options.page,
          options.limit,
        );
        query = query.range(from, to);
      }

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'search habit templates with categories',
      );

      return (result.data as (IHabitTemplate & { category?: any })[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'search habit templates with categories',
        this.entityName,
      );
    }
  }

  /**
   * 根据分类ID获取模板（包含分类信息）
   */
  async findByCategoryWithCategories(
    categoryId: string,
    includeInactive = false,
  ): Promise<(IHabitTemplate & { category?: any })[]> {
    try {
      if (!DatabaseUtils.isValidUUID(categoryId)) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation(
        'FIND_BY_CATEGORY_WITH_CATEGORIES',
        this.tableName,
        {
          categoryId,
          includeInactive,
        },
      );

      let query = this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `,
        )
        .eq('category_id', categoryId);

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      query = query.order('created_at', { ascending: true });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find habit templates by category with categories',
      );

      return (result.data as (IHabitTemplate & { category?: any })[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit templates by category with categories',
        this.entityName,
      );
      return [];
    }
  }

  /**
   * 🚀 性能优化：根据多个分类ID批量获取模板
   */
  async findByCategoryIds(
    categoryIds: string[],
    includeInactive = false,
  ): Promise<(IHabitTemplate & { category?: any })[]> {
    try {
      if (!categoryIds || categoryIds.length === 0) {
        return [];
      }

      // 过滤有效的 UUID
      const validCategoryIds = categoryIds.filter(id => DatabaseUtils.isValidUUID(id));
      if (validCategoryIds.length === 0) {
        return [];
      }

      DatabaseUtils.logDatabaseOperation(
        'FIND_BY_CATEGORY_IDS',
        this.tableName,
        {
          categoryIds: validCategoryIds,
          includeInactive,
        },
      );

      let query = this.supabaseService
        .query(this.tableName)
        .select(
          `
          *,
          category:habit_categories(
            id,
            name,
            icon
          )
        `,
        )
        .in('category_id', validCategoryIds);

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      query = query.order('category_id').order('created_at', { ascending: true });

      const result = await query;

      this.supabaseService.handleDatabaseError(
        result.error,
        'find habit templates by category ids',
      );

      return (result.data as (IHabitTemplate & { category?: any })[]) || [];
    } catch (error) {
      DatabaseUtils.handleError(
        error,
        'find habit templates by category ids',
        this.entityName,
      );
      return [];
    }
  }

  /**
   * 验证创建数据
   */
  protected validateCreateData(data: Partial<IHabitTemplate>): void {
    super.validateCreateData(data);

    if (
      !data.category_id?.trim() ||
      !DatabaseUtils.isValidUUID(data.category_id)
    ) {
      DatabaseUtils.handleError(
        new Error('Valid category ID is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.name?.trim()) {
      DatabaseUtils.handleError(
        new Error('Template name is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.icon?.trim()) {
      DatabaseUtils.handleError(
        new Error('Template icon is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    if (!data.theme_color?.trim()) {
      DatabaseUtils.handleError(
        new Error('Template theme color is required'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }

    // 验证主题色格式（HEX颜色）
    if (data.theme_color && !/^#[0-9A-Fa-f]{6}$/.test(data.theme_color)) {
      DatabaseUtils.handleError(
        new Error('Theme color must be a valid HEX color (e.g., #FF0000)'),
        `validate create ${this.entityName}`,
        this.entityName,
      );
    }
  }
}
