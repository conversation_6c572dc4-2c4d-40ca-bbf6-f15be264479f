import { Test, TestingModule } from '@nestjs/testing';
import { HabitLibraryController } from '../habit-library.controller';
import { HabitCategoryService } from '../../../services/habit-category.service';
import { HabitTemplateService } from '../../../services/habit-template.service';
import { HabitCategoryResponseDto } from '../../../dto/habit-category/habit-category-response.dto';
import { HabitTemplateResponseDto } from '../../../dto/habit-template/habit-template-response.dto';

describe('HabitLibraryController', () => {
  let controller: HabitLibraryController;
  let habitCategoryService: jest.Mocked<HabitCategoryService>;
  let habitTemplateService: jest.Mocked<HabitTemplateService>;

  const mockCategory: HabitCategoryResponseDto = {
    id: 'category-1',
    name: '生活自理',
    icon: 'life-icon',
    sort_order: 1,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockTemplate: HabitTemplateResponseDto = {
    id: 'template-1',
    category_id: 'category-1',
    name: '睡前刷牙',
    icon: 'brush-teeth-icon',
    theme_color: '#FF6B6B',
    description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
    cover: 'https://example.com/cover.jpg',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const mockHabitCategoryService = {
      findAll: jest.fn(),
    };

    const mockHabitTemplateService = {
      findByCategory: jest.fn(),
      findAll: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HabitLibraryController],
      providers: [
        {
          provide: HabitCategoryService,
          useValue: mockHabitCategoryService,
        },
        {
          provide: HabitTemplateService,
          useValue: mockHabitTemplateService,
        },
      ],
    }).compile();

    controller = module.get<HabitLibraryController>(HabitLibraryController);
    habitCategoryService = module.get(HabitCategoryService);
    habitTemplateService = module.get(HabitTemplateService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getHabitLibrary', () => {
    it('should return habit library with categories and templates', async () => {
      habitCategoryService.findAll.mockResolvedValue([mockCategory]);
      habitTemplateService.findByCategory.mockResolvedValue([mockTemplate]);

      const result = await controller.getHabitLibrary();

      expect(result).toEqual({
        categories: [
          {
            id: 'category-1',
            name: '生活自理',
            icon: 'life-icon',
            sort_order: 1,
            templates: [
              {
                id: 'template-1',
                name: '睡前刷牙',
                icon: 'brush-teeth-icon',
                theme_color: '#FF6B6B',
                description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
              },
            ],
          },
        ],
      });

      expect(habitCategoryService.findAll).toHaveBeenCalledWith({
        is_active: true,
      });
      expect(habitTemplateService.findByCategory).toHaveBeenCalledWith(
        'category-1',
        false,
      );
    });

    it('should return empty categories when no active categories exist', async () => {
      habitCategoryService.findAll.mockResolvedValue([]);

      const result = await controller.getHabitLibrary();

      expect(result).toEqual({
        categories: [],
      });
    });

    it('should sort categories by sort_order', async () => {
      const category1 = { ...mockCategory, id: 'cat-1', sort_order: 2 };
      const category2 = { ...mockCategory, id: 'cat-2', sort_order: 1 };

      habitCategoryService.findAll.mockResolvedValue([category1, category2]);
      habitTemplateService.findByCategory.mockResolvedValue([]);

      const result = await controller.getHabitLibrary();

      expect(result.categories[0].id).toBe('cat-2');
      expect(result.categories[1].id).toBe('cat-1');
    });
  });

  describe('getCategories', () => {
    it('should return list of categories without templates', async () => {
      habitCategoryService.findAll.mockResolvedValue([mockCategory]);

      const result = await controller.getCategories();

      expect(result).toEqual([
        {
          id: 'category-1',
          name: '生活自理',
          icon: 'life-icon',
          sort_order: 1,
          templates: [],
        },
      ]);

      expect(habitCategoryService.findAll).toHaveBeenCalledWith({
        is_active: true,
      });
    });
  });

  describe('getTemplatesByCategory', () => {
    it('should return templates for specified category', async () => {
      habitTemplateService.findByCategory.mockResolvedValue([mockTemplate]);

      const result = await controller.getTemplatesByCategory({
        id: 'category-1',
      });

      expect(result).toEqual([
        {
          id: 'template-1',
          name: '睡前刷牙',
          icon: 'brush-teeth-icon',
          theme_color: '#FF6B6B',
          description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        },
      ]);

      expect(habitTemplateService.findByCategory).toHaveBeenCalledWith(
        'category-1',
        false,
      );
    });
  });

  describe('searchTemplates', () => {
    it('should search templates with query parameters', async () => {
      habitTemplateService.findAll.mockResolvedValue([mockTemplate]);

      const query = { search: '刷牙' };
      const result = await controller.searchTemplates(query);

      expect(result).toEqual([
        {
          id: 'template-1',
          name: '睡前刷牙',
          icon: 'brush-teeth-icon',
          theme_color: '#FF6B6B',
          description: '睡前刷干净牙齿，让小牙齿一夜都在休息哦',
        },
      ]);

      expect(habitTemplateService.findAll).toHaveBeenCalledWith({
        search: '刷牙',
        include_inactive: false,
      });
    });
  });
});
