import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { OptionalJwtAuthGuard } from '../../../auth/guards/optional-jwt-auth.guard';
import { HabitCategoryService } from '../../services/habit-category.service';
import { HabitTemplateService } from '../../services/habit-template.service';
import {
  HabitLibraryResponseDto,
  HabitLibraryCategoryDto,
  HabitLibraryTemplateDto,
  HabitCategoryTreeDto,
  HabitSubCategoryTreeDto,
  HabitTemplateInfoDto,
  HabitCategoryTreeSwaggerDto,
} from '../../dto/library/habit-library-response.dto';
import { HabitCategoryQueryDto } from '../../dto/habit-category/habit-category-query.dto';
import { HabitTemplateQueryDto } from '../../dto/habit-template/habit-template-query.dto';
import { HabitTemplateResponseDto } from '../../dto/habit-template/habit-template-response.dto';
import { UuidParamDto } from '../../dto/common/uuid-param.dto';
import { IHabitCategoryTree } from '../../interfaces/habit-category.interface';

@ApiTags('习惯库')
@Controller('habits/library')
@UseGuards(OptionalJwtAuthGuard)
@ApiBearerAuth()
export class HabitLibraryController {
  // 🚀 简单的内存缓存，缓存 5 分钟
  private treeCache: {
    data: HabitCategoryTreeDto[] | null;
    timestamp: number;
    ttl: number;
  } = {
    data: null,
    timestamp: 0,
    ttl: 5 * 60 * 1000, // 5 分钟
  };

  constructor(
    private readonly habitCategoryService: HabitCategoryService,
    private readonly habitTemplateService: HabitTemplateService,
  ) {}

  @Get()
  @ApiOperation({ summary: '获取习惯库（按层级分类显示）' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯库',
    type: HabitLibraryResponseDto,
  })
  async getHabitLibrary(): Promise<HabitLibraryResponseDto> {
    // 获取分类树形结构
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = true;
    const categoryTree =
      await this.habitCategoryService.findCategoryTree(categoryQuery);

    // 构建层级分类结构，只为二级分类（或没有子分类的一级分类）获取模板
    const categoriesWithTemplates: HabitLibraryCategoryDto[] = [];

    for (const topCategory of categoryTree) {
      if (topCategory.children && topCategory.children.length > 0) {
        // 有子分类的一级分类，处理其子分类
        for (const subCategory of topCategory.children) {
          const templates = await this.habitTemplateService.findByCategory(
            subCategory.id,
            false,
          );

          const libraryCategory = new HabitLibraryCategoryDto();
          libraryCategory.id = subCategory.id;
          libraryCategory.name = `${topCategory.name} - ${subCategory.name}`;
          libraryCategory.icon = subCategory.icon;
          libraryCategory.parent_id = subCategory.parent_id;
          libraryCategory.level = subCategory.level;
          libraryCategory.sort_order = subCategory.sort_order;
          libraryCategory.templates = templates.map((template) => {
            const libraryTemplate = new HabitLibraryTemplateDto();
            libraryTemplate.id = template.id;
            libraryTemplate.name = template.name;
            libraryTemplate.icon = template.icon;
            libraryTemplate.theme_color = template.theme_color;
            libraryTemplate.description = template.description;
            return libraryTemplate;
          });

          categoriesWithTemplates.push(libraryCategory);
        }
      } else {
        // 没有子分类的一级分类，直接获取其模板
        const templates = await this.habitTemplateService.findByCategory(
          topCategory.id,
          false,
        );

        const libraryCategory = new HabitLibraryCategoryDto();
        libraryCategory.id = topCategory.id;
        libraryCategory.name = topCategory.name;
        libraryCategory.icon = topCategory.icon;
        libraryCategory.level = topCategory.level;
        libraryCategory.sort_order = topCategory.sort_order;
        libraryCategory.templates = templates.map((template) => {
          const libraryTemplate = new HabitLibraryTemplateDto();
          libraryTemplate.id = template.id;
          libraryTemplate.name = template.name;
          libraryTemplate.icon = template.icon;
          libraryTemplate.theme_color = template.theme_color;
          libraryTemplate.description = template.description;
          return libraryTemplate;
        });

        categoriesWithTemplates.push(libraryCategory);
      }
    }

    const response = new HabitLibraryResponseDto();
    response.categories = categoriesWithTemplates;
    return response;
  }

  @Get('categories')
  @ApiOperation({ summary: '获取习惯分类列表（支持层级）' })
  @ApiResponse({
    status: 200,
    description: '成功获取分类列表',
    type: [HabitLibraryCategoryDto],
  })
  async getCategories(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<HabitLibraryCategoryDto[]> {
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = query.is_active ?? true;
    categoryQuery.level = query.level;
    categoryQuery.parent_id = query.parent_id;

    const categories = await this.habitCategoryService.findAll(categoryQuery);

    return categories.map((category) => {
      const libraryCategory = new HabitLibraryCategoryDto();
      libraryCategory.id = category.id;
      libraryCategory.name = category.name;
      libraryCategory.icon = category.icon;
      libraryCategory.parent_id = category.parent_id;
      libraryCategory.level = category.level;
      libraryCategory.sort_order = category.sort_order;
      libraryCategory.templates = []; // 不包含模板，只返回分类信息
      return libraryCategory;
    });
  }

  @Get('categories/tree')
  @ApiOperation({ summary: '获取分类树形结构' })
  @ApiResponse({
    status: 200,
    description: '成功获取分类树',
    type: 'object',
  })
  async getCategoryTree(
    @Query() query: HabitCategoryQueryDto,
  ): Promise<IHabitCategoryTree[]> {
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = query.is_active ?? true;

    return this.habitCategoryService.findCategoryTree(categoryQuery);
  }

  @Get('templates/:categoryId')
  @ApiOperation({ summary: '获取指定分类下的习惯模板' })
  @ApiResponse({
    status: 200,
    description: '成功获取模板列表',
    type: [HabitLibraryTemplateDto],
  })
  async getTemplatesByCategory(
    @Param() params: UuidParamDto,
  ): Promise<HabitLibraryTemplateDto[]> {
    const templates = await this.habitTemplateService.findByCategory(
      params.id,
      false,
    );

    return templates.map((template) => {
      const libraryTemplate = new HabitLibraryTemplateDto();
      libraryTemplate.id = template.id;
      libraryTemplate.name = template.name;
      libraryTemplate.icon = template.icon;
      libraryTemplate.theme_color = template.theme_color;
      libraryTemplate.description = template.description;
      return libraryTemplate;
    });
  }

  @Get('search')
  @ApiOperation({ summary: '搜索习惯模板' })
  @ApiResponse({
    status: 200,
    description: '成功搜索模板',
    type: [HabitLibraryTemplateDto],
  })
  async searchTemplates(
    @Query() query: HabitTemplateQueryDto,
  ): Promise<HabitLibraryTemplateDto[]> {
    // 确保只搜索活跃的模板
    const searchQuery = { ...query, include_inactive: false };
    const templates = await this.habitTemplateService.findAll(searchQuery);

    return templates.map((template) => {
      const libraryTemplate = new HabitLibraryTemplateDto();
      libraryTemplate.id = template.id;
      libraryTemplate.name = template.name;
      libraryTemplate.icon = template.icon;
      libraryTemplate.theme_color = template.theme_color;
      libraryTemplate.description = template.description;
      return libraryTemplate;
    });
  }

  @Get('template/:id')
  @ApiOperation({ summary: '根据模版ID获取习惯模版详情' })
  @ApiResponse({
    status: 200,
    description: '成功获取模版详情',
    type: HabitTemplateResponseDto,
  })
  async getTemplateById(
    @Param() params: UuidParamDto,
  ): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.findById(params.id);
  }

  @Get('template/key/:key')
  @ApiOperation({ summary: '根据模版key获取习惯模版详情' })
  @ApiResponse({
    status: 200,
    description: '成功获取模版详情',
    type: HabitTemplateResponseDto,
  })
  async getTemplateByKey(
    @Param('key') key: string,
  ): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.findByKey(key);
  }

  @Get('tree')
  @ApiOperation({ summary: '获取完整习惯库树形结构（分类+模板）' })
  @ApiResponse({
    status: 200,
    description: '成功获取习惯库树形结构',
    type: [HabitCategoryTreeSwaggerDto],
  })
  async getHabitLibraryTree(): Promise<HabitCategoryTreeDto[]> {
    // 🚀 检查缓存
    const now = Date.now();
    if (
      this.treeCache.data &&
      (now - this.treeCache.timestamp) < this.treeCache.ttl
    ) {
      console.log('🎯 返回缓存的习惯库数据');
      return this.treeCache.data;
    }

    console.log('🔄 重新获取习惯库数据');

    // 获取分类树形结构
    const categoryQuery = new HabitCategoryQueryDto();
    categoryQuery.is_active = true;
    const categoryTree =
      await this.habitCategoryService.findCategoryTree(categoryQuery);

    if (!categoryTree || categoryTree.length === 0) {
      return [];
    }

    // 🚀 性能优化：一次性获取所有模板，避免 N+1 查询问题
    const allCategoryIds = this.extractAllCategoryIds(categoryTree);
    const allTemplatesMap = await this.getAllTemplatesByCategoryIds(allCategoryIds);

    // 构建树形结构数据
    const treeData: HabitCategoryTreeDto[] = [];

    for (const topCategory of categoryTree) {
      const categoryTreeDto: HabitCategoryTreeDto = {
        id: topCategory.id,
        name: topCategory.name,
        icon: topCategory.icon,
        level: topCategory.level,
        sort_order: topCategory.sort_order,
        children: [],
      };

      if (topCategory.children && topCategory.children.length > 0) {
        // 有子分类的一级分类，处理其子分类
        for (const subCategory of topCategory.children) {
          const templates = allTemplatesMap.get(subCategory.id) || [];

          const subCategoryTreeDto: HabitSubCategoryTreeDto = {
            id: subCategory.id,
            name: subCategory.name,
            icon: subCategory.icon,
            level: subCategory.level,
            sort_order: subCategory.sort_order,
            templates: templates.map(
              (template): HabitTemplateInfoDto => ({
                id: template.id,
                name: template.name,
                icon: template.icon,
                theme_color: template.theme_color,
                description: template.description,
              }),
            ),
          };

          categoryTreeDto.children.push(subCategoryTreeDto);
        }
      } else {
        // 没有子分类的一级分类，创建虚拟的二级分类来包含模板
        const templates = allTemplatesMap.get(topCategory.id) || [];

        if (templates.length > 0) {
          const virtualSubCategory: HabitSubCategoryTreeDto = {
            id: topCategory.id, // 使用一级分类的ID
            name: topCategory.name,
            icon: topCategory.icon,
            level: 2, // 虚拟二级分类
            sort_order: 1,
            templates: templates.map(
              (template): HabitTemplateInfoDto => ({
                id: template.id,
                name: template.name,
                icon: template.icon,
                theme_color: template.theme_color,
                description: template.description,
              }),
            ),
          };

          categoryTreeDto.children.push(virtualSubCategory);
        }
      }

      treeData.push(categoryTreeDto);
    }

    // 🚀 更新缓存
    this.treeCache.data = treeData;
    this.treeCache.timestamp = Date.now();

    return treeData;
  }

  /**
   * 提取所有分类ID（包括一级和二级分类）
   */
  private extractAllCategoryIds(categoryTree: any[]): string[] {
    const categoryIds: string[] = [];

    for (const topCategory of categoryTree) {
      categoryIds.push(topCategory.id);

      if (topCategory.children && topCategory.children.length > 0) {
        for (const subCategory of topCategory.children) {
          categoryIds.push(subCategory.id);
        }
      }
    }

    return [...new Set(categoryIds)]; // 去重
  }

  /**
   * 一次性获取所有分类的模板，返回 Map 结构便于快速查找
   */
  private async getAllTemplatesByCategoryIds(
    categoryIds: string[]
  ): Promise<Map<string, any[]>> {
    if (categoryIds.length === 0) {
      return new Map();
    }

    // 一次性查询所有模板
    const allTemplates = await this.habitTemplateService.findByCategoryIds(categoryIds, false);

    // 按分类ID分组
    const templatesMap = new Map<string, any[]>();

    for (const template of allTemplates) {
      const categoryId = template.category_id;
      if (!templatesMap.has(categoryId)) {
        templatesMap.set(categoryId, []);
      }
      templatesMap.get(categoryId)!.push(template);
    }

    return templatesMap;
  }

  /**
   * 🚀 清除缓存（当数据更新时调用）
   */
  private clearCache(): void {
    this.treeCache.data = null;
    this.treeCache.timestamp = 0;
  }
}
