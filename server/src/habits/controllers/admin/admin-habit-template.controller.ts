import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AdminJwtAuthGuard } from '../../../auth/guards/admin-jwt-auth.guard';
import { HabitTemplateService } from '../../services/habit-template.service';
import { CreateHabitTemplateDto } from '../../dto/habit-template/create-habit-template.dto';
import { UpdateHabitTemplateDto } from '../../dto/habit-template/update-habit-template.dto';
import { HabitTemplateResponseDto } from '../../dto/habit-template/habit-template-response.dto';
import { HabitTemplateQueryDto } from '../../dto/habit-template/habit-template-query.dto';

@Controller('admin/habit-templates')
@UseGuards(AdminJwtAuthGuard)
export class AdminHabitTemplateController {
  constructor(private readonly habitTemplateService: HabitTemplateService) {}

  /**
   * 获取所有习惯模板
   */
  @Get()
  async findAll(
    @Query() query: HabitTemplateQueryDto,
  ): Promise<HabitTemplateResponseDto[]> {
    return this.habitTemplateService.findAll(query);
  }

  /**
   * 根据ID获取习惯模板
   */
  @Get(':id')
  async findById(@Param('id') id: string): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.findById(id);
  }

  /**
   * 根据key获取习惯模板
   */
  @Get('key/:key')
  async findByKey(@Param('key') key: string): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.findByKey(key);
  }

  /**
   * 创建习惯模板
   */
  @Post()
  async create(
    @Body() createDto: CreateHabitTemplateDto,
  ): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.create(createDto);
  }

  /**
   * 更新习惯模板
   */
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateHabitTemplateDto,
  ): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.update(id, updateDto);
  }

  /**
   * 删除习惯模板
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id') id: string): Promise<void> {
    return this.habitTemplateService.delete(id);
  }

  /**
   * 根据分类获取模板
   */
  @Get('category/:categoryId')
  async findByCategory(
    @Param('categoryId') categoryId: string,
    @Query('include_inactive') includeInactive?: boolean,
  ): Promise<HabitTemplateResponseDto[]> {
    return this.habitTemplateService.findByCategory(
      categoryId,
      includeInactive,
    );
  }

  /**
   * 批量创建模板
   */
  @Post('batch')
  async createBatch(
    @Body() templates: CreateHabitTemplateDto[],
  ): Promise<HabitTemplateResponseDto[]> {
    return this.habitTemplateService.createBatch(templates);
  }

  /**
   * 激活模板
   */
  @Post(':id/activate')
  async activate(@Param('id') id: string): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.activate(id);
  }

  /**
   * 停用模板
   */
  @Post(':id/deactivate')
  async deactivate(@Param('id') id: string): Promise<HabitTemplateResponseDto> {
    return this.habitTemplateService.deactivate(id);
  }
}
