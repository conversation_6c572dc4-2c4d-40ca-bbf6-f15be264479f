import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  Matches,
  MaxLength,
  Validate,
} from 'class-validator';
import { TransformSanitizeText, TransformHexColor } from '../transformers';
import { IsValidHexColorConstraint } from '../../validators';

export class CreateHabitTemplateDto {
  @IsString()
  @IsNotEmpty()
  category_id: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  @TransformSanitizeText()
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  key?: string;

  @IsString()
  @IsNotEmpty()
  icon: string;

  @IsString()
  @IsNotEmpty()
  @Validate(IsValidHexColorConstraint)
  @TransformHexColor()
  theme_color: string;

  @IsString()
  @IsOptional()
  @TransformSanitizeText()
  description?: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  cover?: string;

  @IsBoolean()
  @IsOptional()
  is_active?: boolean;
}
