# 习惯模版Key字段功能

## 概述

为习惯模版添加了可选的`key`字段，用于提供一个唯一的标识符，方便前端通过语义化的key来引用特定的习惯模版。

## 数据库更改

### 迁移文件

1. **013_add_key_to_habit_templates.sql** - 添加key字段到habit_templates表
2. **014_update_habit_templates_with_keys.sql** - 为现有模版添加key值

### 字段规格

- **字段名**: `key`
- **类型**: `VARCHAR(100)`
- **约束**: `UNIQUE` (唯一性约束)
- **可选**: 是
- **格式**: 字母、数字、下划线、连字符的组合

## 代码更改

### 接口和类型定义

- `IHabitTemplate` - 添加了`key?: string`字段
- `ICreateHabitTemplate` - 添加了`key?: string`字段  
- `IUpdateHabitTemplate` - 添加了`key?: string`字段

### DTO更新

- `CreateHabitTemplateDto` - 添加key字段验证
- `UpdateHabitTemplateDto` - 添加key字段验证
- `HabitTemplateResponseDto` - 包含key字段
- 所有相关的库响应DTO都已更新

### 验证逻辑

添加了`validateTemplateKey`方法：
- 可选字段（允许为空）
- 长度限制：1-100字符
- 格式限制：只允许字母、数字、下划线、连字符

### 仓库方法

添加了`findByKey(key: string)`方法，用于根据key查找习惯模版。

### 服务方法

添加了`findByKey(key: string)`方法，包含业务逻辑和错误处理。

### API端点

#### 管理员端点
- `GET /admin/habit-templates/key/:key` - 根据key获取习惯模版

#### 客户端端点  
- `GET /habits/library/template/key/:key` - 根据key获取习惯模版详情

## 默认Key值

为现有的习惯模版分配了语义化的key值：

| 习惯名称 | Key值 |
|---------|-------|
| 睡前刷牙 | `brush_teeth_before_bed` |
| 饭前洗手 | `wash_hands_before_meal` |
| 独立吃饭 | `eat_independently` |
| 自己穿鞋 | `put_on_shoes_alone` |
| 上厕所 | `use_toilet` |
| 规律作息 | `regular_sleep_schedule` |
| 户外活动 | `outdoor_activities` |
| 亲子阅读 | `parent_child_reading` |
| 绘画 | `drawing_painting` |
| 拼图/积木 | `puzzle_building_blocks` |
| 分享玩具 | `share_toys` |
| 整理玩具 | `organize_toys` |

## 使用示例

### 创建带key的习惯模版

```json
{
  "category_id": "11111111-1111-1111-1111-111111111112",
  "name": "睡前刷牙",
  "key": "brush_teeth_before_bed",
  "icon": "🦷",
  "theme_color": "#4CAF50",
  "description": "睡前刷干净牙齿，让小牙齿一夜都在休息哦"
}
```

### 通过key查找模版

```javascript
// 管理员端
const template = await fetch('/admin/habit-templates/key/brush_teeth_before_bed');

// 客户端
const template = await fetch('/habits/library/template/key/brush_teeth_before_bed');
```

## 注意事项

1. **唯一性**: key字段具有唯一性约束，不能重复
2. **可选性**: key字段是可选的，现有代码不受影响
3. **格式限制**: 只允许字母、数字、下划线、连字符
4. **向后兼容**: 所有现有功能保持不变
5. **性能**: 为key字段创建了索引，查询性能良好

## 最佳实践

1. **命名规范**: 使用下划线分隔的小写英文，如`brush_teeth_before_bed`
2. **语义化**: key应该能够清楚表达习惯的含义
3. **简洁性**: 尽量保持key简短但有意义
4. **一致性**: 同类习惯使用相似的命名模式