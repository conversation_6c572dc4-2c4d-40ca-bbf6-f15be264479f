import React, { useEffect, useState } from "react";
import { View, Text } from "@tarojs/components";
import {
  Avatar,
  Button,
  ConfigProvider,
  SafeArea,
} from "@nutui/nutui-react-taro";
import "./index.scss";
import Taro from "@tarojs/taro";
import { getHomeHabitsOverview } from "src/services/habits";
import { TimelineHabitItem } from "../../types/timeline";
import { CircleProgress } from "@nutui/nutui-react-taro";
import dayjs from "dayjs";
import { getAllBabies } from "../babies/services";
import BoyAvatar from "../../assets/avatar-boy.png";
import GirlAvatar from "../../assets/avatar-girl.png";
import '../../app.scss'
import { Steps, Step } from '@nutui/nutui-react-taro'

import libraryIcon from '../../assets/library.png';
import { Image } from "@nutui/nutui-react-taro";


// 计算月龄的函数
const calculateAgeInMonths = (birthDate: string): { months: number; days: number; totalDays: number } => {
  const birth = dayjs(birthDate);
  const today = dayjs();

  // 计算总天数
  const totalDays = today.diff(birth, 'day');

  // 计算月数和剩余天数
  let months = 0;
  let currentDate = birth;

  // 逐月计算，直到超过今天
  while (currentDate.add(1, 'month').isBefore(today) || currentDate.add(1, 'month').isSame(today, 'day')) {
    months++;
    currentDate = currentDate.add(1, 'month');
  }

  // 计算剩余天数
  const remainingDays = today.diff(currentDate, 'day');

  return {
    months,
    days: remainingDays,
    totalDays
  };
};

// 格式化年龄显示
const formatAge = (birthDate: string): string => {
  const age = calculateAgeInMonths(birthDate);

  if (age.months === 0) {
    return `${age.days}天`;
  } else if (age.days === 0) {
    return `${age.months}个月`;
  } else {
    return `${age.months}个月${age.days}天`;
  }
};

function Index() {
  const [babies, setBabies] = useState<any[]>([]);
  const defaultBaby = babies.find((b) => b.is_default);

  const [overview, setOverview] = useState({
    today_completed_count: 0,
    today_completion_rate: 0,
    today_total_count: 0,
    today_timeline: [],
  });

  useEffect(() => {
    getAllBabies().then((res: any) => setBabies(res.data));
    getHomeHabitsOverview().then((res: any) => setOverview(res.data));
  }, []);

  const handleHabitClick = (habit: TimelineHabitItem) => {
    console.log("点击习惯:", habit);
    Taro.navigateTo({
      url: `/pages/habits/detail/index?id=${habit.habit_id}`,
    });
  };

  return (

    <View className="home-page">

      <View className="overview-info">
        <View className="header-avatar">
          <Avatar
            size="large"
            src={
              defaultBaby?.avatar_url ??
              `${defaultBaby?.gender === 1 ? BoyAvatar : GirlAvatar}`
            }
          />
          <View>
            <div>
              {defaultBaby?.nickname}
            </div>
            {defaultBaby?.birth_date && (
              <div> {formatAge(defaultBaby.birth_date)}</div>
            )}
          </View>
         
        </View>

        <CircleProgress
          radius="30"
          percent={overview.today_completion_rate}
          strokeWidth={10}
          color="#77C2C7"
        >
          {Math.round(overview.today_completion_rate * 100)}%
        </CircleProgress>
      </View>

      <div className="flex justify-between items-center mb-2">
        <span className="font-bold text-lg">今日打卡</span>
        
        <Image
          src={libraryIcon}
          mode="aspectFit"
          width={20}
          height={20}
          onClick={()=>{
            Taro.navigateTo({
              url: `/pages/habits/library/index`,
            });
          }}
        />
      </div>

      <Steps
        dot
        direction="vertical"
      >
        {
          overview.today_timeline.map((habit, index) => {
            return (
              <Step
                value={index}
                // title={habit?.habit_name}
                title={
                <div className="w-full p-3 rounded-lg bg-white mt-2 flex justify-between items-center"
                >
                    <Image
                      src={require(`../../assets/habits/English.png`)}
                      mode="aspectFit"
                      width={40}
                      height={40}
                    />
                    <div className="flex-1 ml-2">
                      <div>
                        {habit?.habit_name}
                      </div>
                      <div className="text-xs text-gray-400">
                        {habit?.preferred_time ?? ' '}
                      </div>
                    </div>
                </div>}
              />
            );
          })
        }
      </Steps>

      {/* <TabBar current="home" /> */}
    </View>
  );
}

export default Index;
