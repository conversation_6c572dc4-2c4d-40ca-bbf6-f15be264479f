# 时间轴组件 (Timeline Component)

## 概述

我已经为你创建了一个美观的时间轴组件，完全符合你提供的设计图样式。该组件可以展示按时间排序的习惯列表，支持完成状态切换和丰富的交互功能。

## 🎯 主要特性

- ✅ **时间自动排序**: 根据 `preferred_time` 字段自动排序，无时间的习惯排在最后
- 🎨 **主题色支持**: 每个习惯可以设置独立的主题颜色
- 📱 **移动端优化**: 响应式设计，完美适配小程序
- 🎭 **丰富图标**: 内置多种emoji图标映射
- ⚡ **交互反馈**: 点击动画、完成状态切换等
- 🎪 **视觉效果**: 渐变背景、阴影、圆角等现代化设计

## 📁 文件结构

```
src/components/timeline/
├── index.tsx          # 主组件文件
├── index.scss         # 样式文件
└── README.md          # 组件文档

src/types/
└── timeline.ts        # TypeScript 类型定义

src/pages/timeline-demo/
├── index.tsx          # 演示页面
└── index.scss         # 演示页面样式
```

## 🚀 快速开始

### 1. 数据格式

你提供的数据格式已完美支持：

```json
[
  {
    "habit_id": "bff4349d-459d-4b0c-be69-a43c2fff1f4c",
    "habit_name": "测试",
    "habit_icon": "apple",
    "theme_color": "#77C2C7",
    "is_completed": false,
    "preferred_time": "06:00:00"
  },
  {
    "habit_id": "f5e2ba2d-cf2e-425b-adae-67ee893dd4e8",
    "habit_name": "刷牙",
    "habit_icon": "rabbit",
    "theme_color": "#D4A574",
    "is_completed": true,
    "preferred_time": "20:00:00"
  },
  {
    "habit_id": "e8831895-b194-4caa-a696-16a1b9fcd959",
    "habit_name": "户外",
    "habit_icon": "ball",
    "theme_color": "#A8D8D8",
    "is_completed": false,
    "preferred_time": null
  }
]
```

### 2. 使用组件

```tsx
import Timeline from '../../components/timeline'
import { TimelineHabitItem } from '../../types/timeline'

const MyPage: React.FC = () => {
  const [habits, setHabits] = useState<TimelineHabitItem[]>(yourHabitsData)

  const handleHabitClick = (habit: TimelineHabitItem) => {
    // 处理习惯点击事件
    console.log('点击了习惯:', habit.habit_name)
  }

  const handleToggleComplete = (habitId: string, isCompleted: boolean) => {
    // 处理完成状态切换
    setHabits(prevHabits =>
      prevHabits.map(habit =>
        habit.habit_id === habitId
          ? { ...habit, is_completed: isCompleted }
          : habit
      )
    )
  }

  return (
    <Timeline
      habits={habits}
      onHabitClick={handleHabitClick}
      onToggleComplete={handleToggleComplete}
    />
  )
}
```

## 🎨 设计特点

### 视觉效果
- 左侧彩色时间标签
- 渐变背景卡片
- 左边框颜色指示器
- 圆形完成状态按钮
- 编辑和菜单操作按钮
- 浮动添加按钮

### 交互体验
- 点击缩放动画
- 完成状态视觉反馈
- 已完成习惯显示删除线
- 按钮点击反馈

## 🔧 自定义配置

### 图标映射
组件内置了丰富的图标映射，你可以在 `getIconEmoji` 函数中添加更多图标：

```typescript
const iconMap: { [key: string]: string } = {
  'apple': '🍎',
  'rabbit': '🐰',
  'ball': '⚽',
  // 添加更多图标...
}
```

### 主题色
每个习惯的 `theme_color` 字段会自动应用到：
- 左边框颜色
- 图标背景色
- 完成按钮颜色
- 卡片背景渐变

## 📱 查看演示

访问 `pages/timeline-demo/index` 页面查看完整的演示效果。

## 🎯 排序规则

1. 有 `preferred_time` 的习惯按时间升序排列 (06:00 → 20:00)
2. `preferred_time` 为 `null` 的习惯排在最后，显示为"待定"
3. 相同时间的习惯会分组在一起

这个时间轴组件完全符合你的设计要求，支持你提供的数据格式，并且具有良好的扩展性和用户体验！
